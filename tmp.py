import requests

cookies = {
    '_data_chl': 'key=www.bing.com',
    'Hm_lvt_d6ce81bd19f02260e0083511abc1c981': '**********',
    'HMACCOUNT': '****************',
    'ASP.NET_SessionId': '0fzwqohnpi4zuluhw1sgabaz',
    'Hm_lpvt_d6ce81bd19f02260e0083511abc1c981': '**********',
    'YOUWANTDATA': 'UserId=efd59d53d9a34b40&Password=fea415cd01715474ffe875c7b912c916&ChildUserId=103dad732c906557&ClientId=0591007093254dc9afbd0def19c4c684',
    '_uetsid': 'f8c46860713a11f0b3c91798ff30f60e|5z0s38|2|fy6|0|2042',
    '_uetvid': 'f8c44aa0713a11f0a337cbf84d127bdf|m5z5la|*************|1|1|bat.bing.com/p/conversions/c/a',
    'tfstk': 'gvpSQp4twz4WqEOON9o4Cgr1K96BOmkaFkspjHezvTB8JyKOuu7P48WBOhb2U6WJdI2BjHvrq9olE3XhpVuZQJtkq9bigl8wdtIAxMkU98FREtQ52-1sQA-k2fF-vmMwLH8G3M7dp_QJDnQcA8IpJ_KYDZjhvzILemtAoZIdv9eLHtINf8IpJ9nXDZjdpwLdwmtAoMBdJvR0hMF5A3isfswAqRi2VZwLpKsx-gxSZRsyUg15W3p7pJFOV__92Z3R2v2hMHfBKk2Cy3dM-GL8yV5kvITfV9M4wOKX9FsDeAz5mQ-vbZK_XRKPFC91eQg0iT8WHtpJ1kepcT_lML5bPq_9EnpGHsktfnpDz3vXYkHd0F7vqLBIBcX5eaBfmpu0ewOv9L5lKz3NZHdXlg6O4UyN5Kx0Oo1gdi_ZcmN3tV-9t-wIk9hdwijS7mibl6fRmi_ZcmN3t_IcVhojcr1h.',
}

headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
    'Connection': 'keep-alive',
    'Origin': 'https://www.youwant.cn',
    'Referer': 'https://www.youwant.cn/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': '_data_chl=key=www.bing.com; Hm_lvt_d6ce81bd19f02260e0083511abc1c981=**********; HMACCOUNT=****************; ASP.NET_SessionId=0fzwqohnpi4zuluhw1sgabaz; Hm_lpvt_d6ce81bd19f02260e0083511abc1c981=**********; YOUWANTDATA=UserId=efd59d53d9a34b40&Password=fea415cd01715474ffe875c7b912c916&ChildUserId=103dad732c906557&ClientId=0591007093254dc9afbd0def19c4c684; _uetsid=f8c46860713a11f0b3c91798ff30f60e|5z0s38|2|fy6|0|2042; _uetvid=f8c44aa0713a11f0a337cbf84d127bdf|m5z5la|*************|1|1|bat.bing.com/p/conversions/c/a; tfstk=gvpSQp4twz4WqEOON9o4Cgr1K96BOmkaFkspjHezvTB8JyKOuu7P48WBOhb2U6WJdI2BjHvrq9olE3XhpVuZQJtkq9bigl8wdtIAxMkU98FREtQ52-1sQA-k2fF-vmMwLH8G3M7dp_QJDnQcA8IpJ_KYDZjhvzILemtAoZIdv9eLHtINf8IpJ9nXDZjdpwLdwmtAoMBdJvR0hMF5A3isfswAqRi2VZwLpKsx-gxSZRsyUg15W3p7pJFOV__92Z3R2v2hMHfBKk2Cy3dM-GL8yV5kvITfV9M4wOKX9FsDeAz5mQ-vbZK_XRKPFC91eQg0iT8WHtpJ1kepcT_lML5bPq_9EnpGHsktfnpDz3vXYkHd0F7vqLBIBcX5eaBfmpu0ewOv9L5lKz3NZHdXlg6O4UyN5Kx0Oo1gdi_ZcmN3tV-9t-wIk9hdwijS7mibl6fRmi_ZcmN3t_IcVhojcr1h.',
}

params = {
    '_': '*************',
}

response = requests.get('https://api.youwant.cn/User/GetUserInfo', params=params, cookies=cookies, headers=headers)

print(response.json())