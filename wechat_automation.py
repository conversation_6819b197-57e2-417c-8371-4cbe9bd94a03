import uiautomation as auto
import time

def add_wechat_contact(account, name):
    """
    微信添加单个好友工具（需提前登录微信）
    :param account: 微信号
    :param name: 好友备注
    """
    # 定位微信主窗口
    wechat_window = auto.WindowControl(
        searchDepth=1,
        ClassName='WeChatMainWndForPC',
        Name='微信'
    )
    
    if not wechat_window.Exists():
        raise RuntimeError("微信主窗口未找到，请确认微信已启动并登录")
    
    # 统一等待时间配置
    wait_sec = 1
    print(f"正在添加: {account}({name})")
    
    try:
        # 进入通讯录
        wechat_window.ButtonControl(Name="通讯录").Click()
        time.sleep(wait_sec)
        
        # 打开添加界面
        wechat_window.ButtonControl(Name="添加朋友").Click()
        time.sleep(wait_sec * 2)  # 此界面加载需要更多时间
        
        # 输入微信号
        search_box = wechat_window.EditControl(Name="微信号/手机号")
        # 获取焦点
        search_box.SetFocus()
        # 全选输入框
        search_box.SendKeys('{Ctrl}a')
        # 输入微信号
        search_box.SendKeys(account)
        time.sleep(wait_sec)
        
        # 查找结果
        if not wechat_window.TextControl(Name=account).Exists():
            print(f"未找到用户: {account}")
            return
        
        wechat_window.TextControl(Name=account).Click()
        time.sleep(wait_sec)
        
        # 填写备注并发送请求
        wechat_window.ButtonControl(Name="添加到通讯录").Click()
        time.sleep(wait_sec)
        
        remarks = wechat_window.EditControl(Name="设置备注")
        if remarks.Exists():
            remarks.SendKeys(name)
            time.sleep(0.5)
            
        wechat_window.ButtonControl(Name="确定").Click()
        time.sleep(wait_sec * 2)  # 等待操作完成
        
        print(f"√ 成功发送好友请求给: {name}")
        
    except Exception as e:
        print(f"添加 {account} 失败: {str(e).split('。')[0]}")
        # 清除可能的残留弹窗
        close_btn = wechat_window.ButtonControl(Name="关闭")
        if close_btn.Exists():
            close_btn.Click()
        time.sleep(wait_sec)

# 使用示例
if __name__ == "__main__":
    # 添加单个好友示例
    add_wechat_contact("example123", "技术好友")