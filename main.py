# -*- coding: utf-8 -*-
# 核心库导入
import os
import sys

# 检测是否为打包后的exe运行
def is_frozen():
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

# 如果是打包后的exe，禁用控制台输出
if is_frozen():
    import io
    sys.stdout = io.StringIO()
    sys.stderr = io.StringIO()

# 第三方库导入
try:
    import webview
except ImportError as e:
    print(f"❌ pywebview库导入失败: {e}")
    print("请安装: pip install pywebview")
    sys.exit(1)

# 本地模块导入
try:
    from apis import API
except ImportError as e:
    print(f"❌ APIs模块导入失败: {e}")
    sys.exit(1)

try:
    from sqlite3_util import init_database
except ImportError as e:
    print(f"❌ 数据库模块导入失败: {e}")
    sys.exit(1)


def main():
    print("[启动] 办公辅助系统...")

    # 初始化数据库
    print("\n[数据库] 初始化数据库...")
    db_init_success = init_database()
    if not db_init_success:
        print("[错误] 数据库初始化失败，程序退出")
        return

    print("\n[API] 初始化API...")
    api = API()
    # 获取web目录和HTML文件路径
    print("\n[界面] 准备启动窗体界面...")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    web_dir = os.path.join(current_dir, "web")
    html_path = os.path.join(web_dir, "pages", "index.html")

    print(f"   Web目录: {web_dir}")
    print(f"   HTML文件: {html_path}")

    # 确保文件存在
    if not os.path.exists(html_path):
        print(f"[错误] GUI文件不存在: {html_path}")
        return
    else:
        print("[成功] GUI文件存在，准备启动...")

    # 创建webview窗口
    print("\n[窗口] 创建应用窗口...")
    webview.create_window(
        title="智能办公系统v1.1(Beta)",
        url=html_path,
        width=1400,
        height=900,
        min_size=(1000, 700),
        resizable=True,
        maximized=False,
        js_api=api
    )
    print("[启动] 启动应用...")
    # 启动webview（开启调试模式）
    webview.start(debug=True)
    print("[关闭] 应用已关闭")


if __name__ == "__main__":
    main()